import React, { useRef, useEffect } from "react";
import { useDispatch } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";
import { closeModal } from "../../../app/common/modals/modalSlice";
import { convertFullName } from "../../../app/common/util/util";

export default function AgentSaveConfirmModal({
  existingPerson,
  newAgentData,
  onSaveChanges,
  onSaveAsNew,
  onDoNotSave
}) {
  const dispatch = useDispatch();
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  function handleSaveChanges() {
    onSaveChanges();
    if (isMountedRef.current) {
      dispatch(closeModal());
    }
  }

  function handleSaveAsNew() {
    onSaveAsNew();
    if (isMountedRef.current) {
      dispatch(closeModal());
    }
  }

  function handleDoNotSave() {
    onDoNotSave();
    if (isMountedRef.current) {
      dispatch(closeModal());
    }
  }
  

  return (
    <Modal
      open={true}
      onClose={() => isMountedRef.current && dispatch(closeModal())}
      size="small"
    >
      <Modal.Header>
        <Header color="orange">
          {/* <Header.Icon name="warning sign" /> */}
          License Number Match Found
        </Header>
      </Modal.Header>
      <Modal.Content>
        <p>
          The license number <strong>{newAgentData.brokerLicenseNumber}</strong> matches 
          an existing contact in your People database.
        </p>
        
        <Divider />
        
        <Header size="small" color="blue">Existing Contact Information:</Header>
        <Grid>
          <Grid.Row>
            <Grid.Column width={8}>
              <strong>Name:</strong> {convertFullName(existingPerson)}
            </Grid.Column>
            <Grid.Column width={8}>
              <strong>Email:</strong> {existingPerson.email || "Not provided"}
            </Grid.Column>
          </Grid.Row>
          <Grid.Row>
            <Grid.Column width={8}>
              <strong>Phone:</strong> {existingPerson.phone || "Not provided"}
            </Grid.Column>
            <Grid.Column width={8}>
              <strong>License #:</strong> {existingPerson.brokerLicenseNumber || "Not provided"}
            </Grid.Column>
          </Grid.Row>
          {existingPerson.brokerageName && (
            <Grid.Row>
              <Grid.Column width={16}>
                <strong>Brokerage:</strong> {existingPerson.brokerageName}
              </Grid.Column>
            </Grid.Row>
          )}
        </Grid>

        <Divider />

        <Header size="small" color="blue">New Agent Information:</Header>
        <Grid>
          <Grid.Row>
            <Grid.Column width={8}>
              <strong>Name:</strong> {convertFullName(newAgentData)}
            </Grid.Column>
            <Grid.Column width={8}>
              <strong>Email:</strong> {newAgentData.email || "Not provided"}
            </Grid.Column>
          </Grid.Row>
          <Grid.Row>
            <Grid.Column width={8}>
              <strong>Phone:</strong> {newAgentData.phone || "Not provided"}
            </Grid.Column>
            <Grid.Column width={8}>
              <strong>License #:</strong> {newAgentData.brokerLicenseNumber || "Not provided"}
            </Grid.Column>
          </Grid.Row>
          {newAgentData.brokerageName && (
            <Grid.Row>
              <Grid.Column width={16}>
                <strong>Brokerage:</strong> {newAgentData.brokerageName}
              </Grid.Column>
            </Grid.Row>
          )}
        </Grid>

        <Divider />

        <p>What would you like to do?</p>
      </Modal.Content>
      <Modal.Actions>
        <Button color="grey" onClick={handleDoNotSave}>
          Do Not Save
        </Button>
        <Button color="green" onClick={handleSaveChanges}>
          Update Existing Contact
        </Button>
        <Button color="blue" onClick={handleSaveAsNew}>
          Save as New Contact
        </Button>
      </Modal.Actions>
    </Modal>
  );
}
